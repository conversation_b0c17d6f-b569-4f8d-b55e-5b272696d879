import { ref, computed, onMounted, onUnmounted } from 'vue'

export interface PerformanceMetrics {
  fps: number
  frameTime: number
  memoryUsage: number
  drawCalls: number
  triangles: number
  geometries: number
  textures: number
}

export interface PerformanceSettings {
  targetFPS: number
  maxParticles: number
  shadowQuality: 'low' | 'medium' | 'high'
  postProcessing: boolean
  antialiasing: boolean
}

export function use3DPerformance() {
  // Performance metrics
  const metrics = ref<PerformanceMetrics>({
    fps: 60,
    frameTime: 16.67,
    memoryUsage: 0,
    drawCalls: 0,
    triangles: 0,
    geometries: 0,
    textures: 0,
  })

  // Performance settings
  const settings = ref<PerformanceSettings>({
    targetFPS: 60,
    maxParticles: 1000,
    shadowQuality: 'medium',
    postProcessing: true,
    antialiasing: true,
  })

  // Device capabilities
  const deviceCapabilities = ref({
    isMobile: false,
    isLowEnd: false,
    webglVersion: 1,
    maxTextureSize: 2048,
    maxVertexUniforms: 1024,
  })

  // Performance monitoring state
  const isMonitoring = ref(false)
  const performanceHistory = ref<number[]>([])
  const lastFrameTime = ref(0)
  const frameCount = ref(0)

  // Adaptive quality
  const adaptiveQuality = computed(() => {
    const avgFPS = performanceHistory.value.length > 0 
      ? performanceHistory.value.reduce((a, b) => a + b, 0) / performanceHistory.value.length 
      : 60

    if (avgFPS < 30) return 'low'
    if (avgFPS < 45) return 'medium'
    return 'high'
  })

  // Quality settings based on performance
  const qualitySettings = computed(() => {
    const quality = adaptiveQuality.value
    
    switch (quality) {
      case 'low':
        return {
          particleCount: Math.floor(settings.value.maxParticles * 0.3),
          shadowQuality: 'low' as const,
          postProcessing: false,
          antialiasing: false,
          lodDistance: 50,
        }
      case 'medium':
        return {
          particleCount: Math.floor(settings.value.maxParticles * 0.6),
          shadowQuality: 'medium' as const,
          postProcessing: true,
          antialiasing: false,
          lodDistance: 100,
        }
      case 'high':
      default:
        return {
          particleCount: settings.value.maxParticles,
          shadowQuality: 'high' as const,
          postProcessing: true,
          antialiasing: true,
          lodDistance: 200,
        }
    }
  })

  // Device detection
  function detectDeviceCapabilities() {
    // Mobile detection
    deviceCapabilities.value.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    )

    // WebGL capabilities
    const canvas = document.createElement('canvas')
    const gl = canvas.getContext('webgl2') || canvas.getContext('webgl')
    
    if (gl) {
      deviceCapabilities.value.webglVersion = gl instanceof WebGL2RenderingContext ? 2 : 1
      deviceCapabilities.value.maxTextureSize = gl.getParameter(gl.MAX_TEXTURE_SIZE)
      deviceCapabilities.value.maxVertexUniforms = gl.getParameter(gl.MAX_VERTEX_UNIFORM_VECTORS)
      
      // Low-end device detection based on capabilities
      deviceCapabilities.value.isLowEnd = 
        deviceCapabilities.value.maxTextureSize < 4096 ||
        deviceCapabilities.value.maxVertexUniforms < 256 ||
        deviceCapabilities.value.isMobile
    }

    // Adjust settings for device capabilities
    if (deviceCapabilities.value.isLowEnd) {
      settings.value.targetFPS = 30
      settings.value.maxParticles = 300
      settings.value.shadowQuality = 'low'
      settings.value.postProcessing = false
      settings.value.antialiasing = false
    }
  }

  // Start performance monitoring
  function startMonitoring() {
    if (isMonitoring.value) return
    
    isMonitoring.value = true
    lastFrameTime.value = performance.now()
    
    const monitor = () => {
      if (!isMonitoring.value) return
      
      const now = performance.now()
      const delta = now - lastFrameTime.value
      
      frameCount.value++
      
      // Update FPS every second
      if (frameCount.value % 60 === 0) {
        const fps = 1000 / delta
        metrics.value.fps = fps
        metrics.value.frameTime = delta
        
        // Keep performance history for adaptive quality
        performanceHistory.value.push(fps)
        if (performanceHistory.value.length > 60) {
          performanceHistory.value.shift()
        }
        
        // Update memory usage if available
        if (performance.memory) {
          metrics.value.memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024
        }
      }
      
      lastFrameTime.value = now
      requestAnimationFrame(monitor)
    }
    
    requestAnimationFrame(monitor)
  }

  // Stop performance monitoring
  function stopMonitoring() {
    isMonitoring.value = false
  }

  // Update renderer info
  function updateRendererInfo(renderer: any) {
    if (renderer && renderer.info) {
      metrics.value.drawCalls = renderer.info.render.calls
      metrics.value.triangles = renderer.info.render.triangles
      metrics.value.geometries = renderer.info.memory.geometries
      metrics.value.textures = renderer.info.memory.textures
    }
  }

  // Get optimal settings for current device
  function getOptimalSettings() {
    return {
      ...settings.value,
      ...qualitySettings.value,
    }
  }

  // Check if feature should be enabled based on performance
  function shouldEnableFeature(feature: string): boolean {
    const quality = adaptiveQuality.value
    
    switch (feature) {
      case 'shadows':
        return quality !== 'low'
      case 'postProcessing':
        return quality === 'high' && settings.value.postProcessing
      case 'antialiasing':
        return quality === 'high' && settings.value.antialiasing
      case 'particles':
        return true // Always enable but adjust count
      case 'bloom':
        return quality !== 'low'
      default:
        return true
    }
  }

  // Performance warning system
  const performanceWarning = computed(() => {
    if (metrics.value.fps < 20) {
      return {
        level: 'critical',
        message: 'Very low FPS detected. Consider reducing quality settings.',
      }
    }
    if (metrics.value.fps < 30) {
      return {
        level: 'warning',
        message: 'Low FPS detected. Some features may be automatically disabled.',
      }
    }
    if (metrics.value.memoryUsage > 500) {
      return {
        level: 'warning',
        message: 'High memory usage detected.',
      }
    }
    return null
  })

  // Initialize on mount
  onMounted(() => {
    detectDeviceCapabilities()
    startMonitoring()
  })

  // Cleanup on unmount
  onUnmounted(() => {
    stopMonitoring()
  })

  return {
    // State
    metrics: readonly(metrics),
    settings,
    deviceCapabilities: readonly(deviceCapabilities),
    adaptiveQuality,
    qualitySettings,
    performanceWarning,
    
    // Methods
    startMonitoring,
    stopMonitoring,
    updateRendererInfo,
    getOptimalSettings,
    shouldEnableFeature,
    detectDeviceCapabilities,
  }
}
