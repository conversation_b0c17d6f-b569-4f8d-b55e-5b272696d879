<template>
  <TresGroup ref="backgroundRef">
    <!-- Particle Systems -->
    <ParticleSystem
      :count="particleSettings.count"
      :color="particleSettings.color"
      :size="particleSettings.size"
      :speed="particleSettings.speed"
      :spread="particleSettings.spread"
      :opacity="particleSettings.opacity"
      :pattern="particleSettings.pattern"
      :animated="animated"
    />
    
    <!-- Secondary particle layer with different color -->
    <ParticleSystem
      :count="particleSettings.count * 0.3"
      :color="particleSettings.secondaryColor"
      :size="particleSettings.size * 0.5"
      :speed="particleSettings.speed * 0.7"
      :spread="particleSettings.spread * 1.2"
      :opacity="particleSettings.opacity * 0.6"
      :pattern="'spiral'"
      :animated="animated"
    />
    
    <!-- Floating Geometry -->
    <FloatingGeometry
      :count="geometrySettings.count"
      :spread="geometrySettings.spread"
      :colors="geometrySettings.colors"
      :float-speed="geometrySettings.floatSpeed"
      :rotation-speed="geometrySettings.rotationSpeed"
      :animated="animated"
      :interactive="interactive"
      @shape-click="onShapeClick"
    />
    
    <!-- Grid lines for cyberpunk aesthetic -->
    <TresGroup v-if="showGrid">
      <TresLineSegments
        v-for="(line, index) in gridLines"
        :key="`grid-${index}`"
      >
        <TresBufferGeometry>
          <TresBufferAttribute
            attach="attributes-position"
            :array="line.positions"
            :count="line.positions.length / 3"
            :item-size="3"
          />
        </TresBufferGeometry>
        <TresLineBasicMaterial
          :color="gridColor"
          :opacity="gridOpacity"
          :transparent="true"
        />
      </TresLineSegments>
    </TresGroup>
    
    <!-- Ambient lighting for the background -->
    <TresAmbientLight :intensity="ambientIntensity" :color="ambientColor" />
  </TresGroup>
</template>

<script setup lang="ts">
import { useRenderLoop } from '@tresjs/core'
import { use3DPerformance } from '@/composables/use3DPerformance'
import ParticleSystem from '@/components/3d/base/ParticleSystem.vue'
import FloatingGeometry from '@/components/3d/effects/FloatingGeometry.vue'

interface Props {
  animated?: boolean
  interactive?: boolean
  theme?: 'neon-lime' | 'neon-cyan' | 'neon-pink' | 'neon-blue' | 'mixed'
  intensity?: number
  showGrid?: boolean
  gridOpacity?: number
  ambientIntensity?: number
}

const props = withDefaults(defineProps<Props>(), {
  animated: true,
  interactive: true,
  theme: 'mixed',
  intensity: 1.0,
  showGrid: true,
  gridOpacity: 0.1,
  ambientIntensity: 0.2,
})

const backgroundRef = ref()

// Performance monitoring
const { qualitySettings, shouldEnableFeature } = use3DPerformance()

// Theme color palettes
const themeColors = {
  'neon-lime': {
    primary: '#00ff41',
    secondary: '#39ff14',
    accent: '#7fff00',
    grid: '#00ff41',
    ambient: '#004d0f',
  },
  'neon-cyan': {
    primary: '#00ffff',
    secondary: '#40e0d0',
    accent: '#00ced1',
    grid: '#00ffff',
    ambient: '#004d4d',
  },
  'neon-pink': {
    primary: '#ff006e',
    secondary: '#ff1493',
    accent: '#ff69b4',
    grid: '#ff006e',
    ambient: '#4d001a',
  },
  'neon-blue': {
    primary: '#0066ff',
    secondary: '#1e90ff',
    accent: '#00bfff',
    grid: '#0066ff',
    ambient: '#001a4d',
  },
  'mixed': {
    primary: '#00ff41',
    secondary: '#00ffff',
    accent: '#ff006e',
    grid: '#0066ff',
    ambient: '#0d0d0d',
  },
}

// Current theme colors
const currentTheme = computed(() => themeColors[props.theme])

// Particle settings based on performance and theme
const particleSettings = computed(() => ({
  count: shouldEnableFeature('particles') ? qualitySettings.value.particleCount : 0,
  color: currentTheme.value.primary,
  secondaryColor: currentTheme.value.secondary,
  size: 1.5 * props.intensity,
  speed: 0.3,
  spread: 60,
  opacity: 0.6 * props.intensity,
  pattern: 'random' as const,
}))

// Geometry settings
const geometrySettings = computed(() => ({
  count: shouldEnableFeature('particles') ? Math.floor(qualitySettings.value.particleCount / 20) : 0,
  spread: 40,
  colors: [
    currentTheme.value.primary,
    currentTheme.value.secondary,
    currentTheme.value.accent,
  ],
  floatSpeed: 0.3,
  rotationSpeed: 0.2,
}))

// Grid settings
const gridColor = computed(() => currentTheme.value.grid)
const ambientColor = computed(() => currentTheme.value.ambient)

// Grid lines for cyberpunk aesthetic
const gridLines = ref<Array<{ positions: Float32Array }>>([])

function generateGridLines() {
  if (!props.showGrid || !shouldEnableFeature('particles')) {
    gridLines.value = []
    return
  }
  
  const lines: Array<{ positions: Float32Array }> = []
  const size = 50
  const divisions = 10
  const step = size / divisions
  
  // Horizontal lines
  for (let i = 0; i <= divisions; i++) {
    const y = (i - divisions / 2) * step
    const positions = new Float32Array([
      -size / 2, y, 0,
      size / 2, y, 0,
    ])
    lines.push({ positions })
  }
  
  // Vertical lines
  for (let i = 0; i <= divisions; i++) {
    const x = (i - divisions / 2) * step
    const positions = new Float32Array([
      x, -size / 2, 0,
      x, size / 2, 0,
    ])
    lines.push({ positions })
  }
  
  gridLines.value = lines
}

// Animation state
const time = ref(0)

// Update background effects
function updateBackground(delta: number) {
  if (!props.animated) return
  
  time.value += delta
  
  // Animate grid opacity for pulsing effect
  if (props.showGrid && backgroundRef.value) {
    const pulseOpacity = props.gridOpacity * (0.5 + 0.5 * Math.sin(time.value * 0.5))
    // Grid opacity animation would be handled by individual line materials
  }
}

// Interaction handlers
function onShapeClick(event: { index: number; shape: any }) {
  emit('backgroundInteraction', {
    type: 'shapeClick',
    data: event,
  })
}

// Render loop
const { onLoop } = useRenderLoop()

onLoop(({ delta }) => {
  updateBackground(delta)
})

// Initialize on mount
onMounted(() => {
  generateGridLines()
})

// Regenerate grid when settings change
watch([() => props.showGrid, qualitySettings], () => {
  generateGridLines()
})

// Emit events
const emit = defineEmits<{
  backgroundInteraction: [{ type: string; data: any }]
}>()

defineExpose({
  background: backgroundRef,
  particleSettings,
  geometrySettings,
  currentTheme,
})
</script>
