<template>
  <TresGroup ref="groupRef">
    <TresMesh
      v-for="(shape, index) in shapes"
      :key="index"
      ref="meshRefs"
      :position="shape.position"
      :rotation="shape.rotation"
      :scale="shape.scale"
      @click="onShapeClick(index)"
      @pointer-enter="onShapeHover(index, true)"
      @pointer-leave="onShapeHover(index, false)"
    >
      <!-- Dynamic geometry based on shape type -->
      <TresBoxGeometry v-if="shape.type === 'cube'" :args="[1, 1, 1]" />
      <TresSphereGeometry v-else-if="shape.type === 'sphere'" :args="[0.5, 16, 16]" />
      <TresOctahedronGeometry v-else-if="shape.type === 'octahedron'" :args="[0.6]" />
      <TresTetrahedronGeometry v-else-if="shape.type === 'tetrahedron'" :args="[0.7]" />
      <TresConeGeometry v-else-if="shape.type === 'cone'" :args="[0.5, 1, 8]" />
      <TresCylinderGeometry v-else :args="[0.3, 0.3, 1, 8]" />
      
      <!-- Neon material with cyberpunk glow -->
      <NeonMaterial
        :color="shape.color"
        :intensity="shape.intensity"
        :glow-strength="shape.glowStrength"
        :pulse-speed="shape.pulseSpeed"
        :animated="animated"
      />
    </TresMesh>
  </TresGroup>
</template>

<script setup lang="ts">
import { useRenderLoop } from '@tresjs/core'
import { use3DPerformance } from '@/composables/use3DPerformance'
import NeonMaterial from '@/components/3d/base/NeonMaterial.vue'
import gsap from 'gsap'

interface Shape {
  type: 'cube' | 'sphere' | 'octahedron' | 'tetrahedron' | 'cone' | 'cylinder'
  position: [number, number, number]
  rotation: [number, number, number]
  scale: [number, number, number]
  color: string
  intensity: number
  glowStrength: number
  pulseSpeed: number
  velocity: [number, number, number]
  rotationSpeed: [number, number, number]
  originalPosition: [number, number, number]
}

interface Props {
  count?: number
  spread?: number
  animated?: boolean
  interactive?: boolean
  colors?: string[]
  floatSpeed?: number
  rotationSpeed?: number
  hoverEffect?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  count: 12,
  spread: 20,
  animated: true,
  interactive: true,
  colors: () => ['#00ff41', '#00ffff', '#ff006e', '#0066ff', '#39ff14', '#ff073a'],
  floatSpeed: 0.5,
  rotationSpeed: 0.3,
  hoverEffect: true,
})

const groupRef = ref()
const meshRefs = ref<any[]>([])

// Performance monitoring
const { qualitySettings, shouldEnableFeature } = use3DPerformance()

// Reactive shape count based on performance
const shapeCount = computed(() => {
  if (!shouldEnableFeature('particles')) return 0
  return Math.min(props.count, Math.floor(qualitySettings.value.particleCount / 10))
})

// Shape data
const shapes = ref<Shape[]>([])
const time = ref(0)

// Shape types for variety
const shapeTypes: Shape['type'][] = ['cube', 'sphere', 'octahedron', 'tetrahedron', 'cone', 'cylinder']

// Initialize shapes
function initializeShapes() {
  shapes.value = []
  
  for (let i = 0; i < shapeCount.value; i++) {
    const type = shapeTypes[Math.floor(Math.random() * shapeTypes.length)]
    const color = props.colors[Math.floor(Math.random() * props.colors.length)]
    
    // Random position within spread
    const x = (Math.random() - 0.5) * props.spread
    const y = (Math.random() - 0.5) * props.spread
    const z = (Math.random() - 0.5) * props.spread
    
    // Random rotation
    const rotX = Math.random() * Math.PI * 2
    const rotY = Math.random() * Math.PI * 2
    const rotZ = Math.random() * Math.PI * 2
    
    // Random scale with some variation
    const scale = 0.5 + Math.random() * 0.8
    
    shapes.value.push({
      type,
      position: [x, y, z],
      rotation: [rotX, rotY, rotZ],
      scale: [scale, scale, scale],
      color,
      intensity: 0.8 + Math.random() * 0.4,
      glowStrength: 1.5 + Math.random() * 1.0,
      pulseSpeed: 0.5 + Math.random() * 1.0,
      velocity: [
        (Math.random() - 0.5) * props.floatSpeed,
        (Math.random() - 0.5) * props.floatSpeed,
        (Math.random() - 0.5) * props.floatSpeed,
      ],
      rotationSpeed: [
        (Math.random() - 0.5) * props.rotationSpeed,
        (Math.random() - 0.5) * props.rotationSpeed,
        (Math.random() - 0.5) * props.rotationSpeed,
      ],
      originalPosition: [x, y, z],
    })
  }
}

// Update animation
function updateShapes(delta: number) {
  if (!props.animated || shapes.value.length === 0) return
  
  time.value += delta
  
  shapes.value.forEach((shape, index) => {
    const mesh = meshRefs.value[index]
    if (!mesh) return
    
    // Floating motion
    shape.position[0] = shape.originalPosition[0] + Math.sin(time.value * 0.5 + index) * 2
    shape.position[1] = shape.originalPosition[1] + Math.cos(time.value * 0.3 + index) * 1.5
    shape.position[2] = shape.originalPosition[2] + Math.sin(time.value * 0.4 + index * 0.5) * 1
    
    // Rotation animation
    shape.rotation[0] += shape.rotationSpeed[0] * delta
    shape.rotation[1] += shape.rotationSpeed[1] * delta
    shape.rotation[2] += shape.rotationSpeed[2] * delta
    
    // Update mesh transform
    mesh.position.set(...shape.position)
    mesh.rotation.set(...shape.rotation)
  })
}

// Interaction handlers
function onShapeClick(index: number) {
  if (!props.interactive) return
  
  const mesh = meshRefs.value[index]
  if (!mesh) return
  
  // Pulse animation on click
  gsap.to(mesh.scale, {
    x: 1.5,
    y: 1.5,
    z: 1.5,
    duration: 0.2,
    yoyo: true,
    repeat: 1,
    ease: "power2.out"
  })
  
  // Emit click event
  emit('shapeClick', { index, shape: shapes.value[index] })
}

function onShapeHover(index: number, isHovering: boolean) {
  if (!props.hoverEffect) return
  
  const mesh = meshRefs.value[index]
  if (!mesh) return
  
  if (isHovering) {
    gsap.to(mesh.scale, {
      x: 1.2,
      y: 1.2,
      z: 1.2,
      duration: 0.3,
      ease: "power2.out"
    })
  } else {
    gsap.to(mesh.scale, {
      x: shapes.value[index].scale[0],
      y: shapes.value[index].scale[1],
      z: shapes.value[index].scale[2],
      duration: 0.3,
      ease: "power2.out"
    })
  }
}

// Render loop
const { onLoop } = useRenderLoop()

onLoop(({ delta }) => {
  updateShapes(delta)
})

// Initialize on mount
onMounted(() => {
  initializeShapes()
})

// Reinitialize when count changes
watch(shapeCount, () => {
  initializeShapes()
})

// Emit events
const emit = defineEmits<{
  shapeClick: [{ index: number; shape: Shape }]
  shapeHover: [{ index: number; shape: Shape; isHovering: boolean }]
}>()

defineExpose({
  group: groupRef,
  meshes: meshRefs,
  shapes,
  initializeShapes,
})
</script>
