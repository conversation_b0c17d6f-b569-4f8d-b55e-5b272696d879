<template>
  <div ref="containerRef" class="relative w-full h-full">
    <!-- WebGL Support Check -->
    <div v-if="!webglSupported" class="flex items-center justify-center w-full h-full bg-brutal-charcoal border-brutal">
      <div class="text-center p-brutal-xl">
        <h3 class="font-brutal text-xl text-brutal-white mb-4">3D NOT SUPPORTED</h3>
        <p class="font-mono-brutal text-sm text-brutal-white">
          Your device doesn't support WebGL or 3D acceleration.
        </p>
        <slot name="fallback" />
      </div>
    </div>

    <!-- Reduced Motion Fallback -->
    <div v-else-if="prefersReducedMotion && !force3D" class="flex items-center justify-center w-full h-full bg-brutal-charcoal border-brutal">
      <div class="text-center p-brutal-xl">
        <h3 class="font-brutal text-xl text-brutal-white mb-4">3D DISABLED</h3>
        <p class="font-mono-brutal text-sm text-brutal-white mb-4">
          3D effects are disabled due to your motion preferences.
        </p>
        <Button 
          @click="force3D = true"
          class="border-brutal bg-acid-green text-brutal-black font-brutal text-sm px-4 py-2 hover-brutal-electric"
        >
          ENABLE 3D
        </Button>
        <slot name="fallback" />
      </div>
    </div>

    <!-- 3D Scene -->
    <TresCanvas
      v-else
      ref="canvasRef"
      :alpha="alpha"
      :antialias="antialias"
      :shadows="shadows"
      :tone-mapping="toneMapping"
      :shadow-map-type="shadowMapType"
      :output-color-space="outputColorSpace"
      :clear-color="clearColor"
      :pixel-ratio="pixelRatio"
      :window-size="windowSize"
      class="w-full h-full"
      @created="onCanvasCreated"
      @render="onRender"
    >
      <!-- Default Lighting Setup -->
      <TresAmbientLight :intensity="ambientLightIntensity" color="#ffffff" />
      <TresDirectionalLight
        :position="[10, 10, 5]"
        :intensity="directionalLightIntensity"
        :cast-shadow="shadows"
        :shadow-map-size="[2048, 2048]"
        color="#ffffff"
      />

      <!-- Camera Setup -->
      <TresPerspectiveCamera
        ref="cameraRef"
        :position="cameraPosition"
        :fov="cameraFov"
        :near="0.1"
        :far="1000"
        :look-at="cameraTarget"
      />

      <!-- Scene Content -->
      <slot />

      <!-- Performance Monitor (Development Only) -->
      <TresGroup v-if="showStats && isDev">
        <!-- Stats will be added via composable -->
      </TresGroup>
    </TresCanvas>

    <!-- Performance Stats Overlay -->
    <div 
      v-if="showStats && isDev" 
      class="absolute top-4 left-4 bg-brutal-black border-brutal p-2 font-mono-brutal text-xs text-acid-green z-50"
    >
      <div>FPS: {{ Math.round(fps) }}</div>
      <div>Frame Time: {{ Math.round(frameTime) }}ms</div>
      <div>Memory: {{ Math.round(memoryUsage) }}MB</div>
      <div>Draw Calls: {{ drawCalls }}</div>
    </div>

    <!-- Loading Overlay -->
    <div 
      v-if="isLoading" 
      class="absolute inset-0 flex items-center justify-center bg-brutal-black bg-opacity-75 z-40"
    >
      <div class="text-center">
        <div class="font-brutal text-xl text-acid-green mb-2 glitch">LOADING 3D...</div>
        <div class="w-32 h-1 bg-brutal-charcoal border-brutal">
          <div 
            class="h-full bg-acid-green transition-all duration-300"
            :style="{ width: `${loadingProgress}%` }"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { TresCanvas } from '@tresjs/core'
import { useDevicePixelRatio, useWindowSize, usePreferredReducedMotion } from '@vueuse/core'
import { PCFSoftShadowMap, SRGBColorSpace, ACESFilmicToneMapping } from 'three'

interface Props {
  alpha?: boolean
  antialias?: boolean
  shadows?: boolean
  clearColor?: string
  showStats?: boolean
  ambientLightIntensity?: number
  directionalLightIntensity?: number
  cameraPosition?: [number, number, number]
  cameraTarget?: [number, number, number]
  cameraFov?: number
}

const props = withDefaults(defineProps<Props>(), {
  alpha: true,
  antialias: true,
  shadows: true,
  clearColor: '#000000',
  showStats: false,
  ambientLightIntensity: 0.3,
  directionalLightIntensity: 0.8,
  cameraPosition: () => [0, 0, 5],
  cameraTarget: () => [0, 0, 0],
  cameraFov: 75,
})

// Refs
const containerRef = ref<HTMLElement>()
const canvasRef = ref()
const cameraRef = ref()

// Reactive properties
const { width, height } = useWindowSize()
const { pixelRatio } = useDevicePixelRatio()
const prefersReducedMotion = usePreferredReducedMotion()

// 3D Settings
const toneMapping = ACESFilmicToneMapping
const shadowMapType = PCFSoftShadowMap
const outputColorSpace = SRGBColorSpace
const windowSize = computed(() => ({ width: width.value, height: height.value }))

// State
const webglSupported = ref(true)
const force3D = ref(false)
const isLoading = ref(true)
const loadingProgress = ref(0)
const isDev = process.dev

// Performance monitoring
const fps = ref(60)
const frameTime = ref(16.67)
const memoryUsage = ref(0)
const drawCalls = ref(0)
const lastTime = ref(0)
const frameCount = ref(0)

// WebGL Detection
onMounted(() => {
  checkWebGLSupport()
  startPerformanceMonitoring()
})

function checkWebGLSupport() {
  try {
    const canvas = document.createElement('canvas')
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
    webglSupported.value = !!gl
    
    if (!gl) {
      console.warn('WebGL not supported')
      return
    }

    // Check for required extensions
    const extensions = [
      'OES_texture_float',
      'OES_texture_half_float',
      'WEBGL_depth_texture'
    ]
    
    extensions.forEach(ext => {
      if (!gl.getExtension(ext)) {
        console.warn(`WebGL extension ${ext} not supported`)
      }
    })
  } catch (error) {
    console.error('WebGL detection failed:', error)
    webglSupported.value = false
  }
}

function onCanvasCreated({ renderer, scene, camera }) {
  console.log('TresJS Canvas created')
  
  // Configure renderer for cyberpunk aesthetic
  renderer.shadowMap.enabled = props.shadows
  renderer.shadowMap.type = shadowMapType
  renderer.toneMapping = toneMapping
  renderer.toneMappingExposure = 1.2
  renderer.outputColorSpace = outputColorSpace
  
  // Simulate loading progress
  simulateLoading()
}

function onRender({ renderer, scene, camera, delta }) {
  updatePerformanceStats(delta)
}

function simulateLoading() {
  const interval = setInterval(() => {
    loadingProgress.value += Math.random() * 20
    if (loadingProgress.value >= 100) {
      loadingProgress.value = 100
      setTimeout(() => {
        isLoading.value = false
      }, 500)
      clearInterval(interval)
    }
  }, 100)
}

function startPerformanceMonitoring() {
  if (!isDev) return
  
  const updateStats = () => {
    const now = performance.now()
    frameCount.value++
    
    if (now - lastTime.value >= 1000) {
      fps.value = (frameCount.value * 1000) / (now - lastTime.value)
      frameCount.value = 0
      lastTime.value = now
      
      // Memory usage (approximate)
      if (performance.memory) {
        memoryUsage.value = performance.memory.usedJSHeapSize / 1024 / 1024
      }
    }
    
    requestAnimationFrame(updateStats)
  }
  
  requestAnimationFrame(updateStats)
}

function updatePerformanceStats(delta: number) {
  frameTime.value = delta * 1000
}

// Expose camera for external control
defineExpose({
  camera: cameraRef,
  canvas: canvasRef,
  container: containerRef,
})
</script>
