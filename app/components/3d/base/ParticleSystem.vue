<template>
  <TresGroup ref="groupRef">
    <TresPoints ref="pointsRef">
      <TresBufferGeometry ref="geometryRef" />
      <TresPointsMaterial
        ref="materialRef"
        :size="props.size"
        :transparent="true"
        :opacity="props.opacity"
        :blending="AdditiveBlending"
        :vertex-colors="true"
        :size-attenuation="true"
      />
    </TresPoints>
  </TresGroup>
</template>

<script setup lang="ts">
import {
  BufferGeometry,
  BufferAttribute,
  PointsMaterial,
  AdditiveBlending,
  Color
} from 'three'
import { useRenderLoop } from '@tresjs/core'
import { use3DPerformance } from '@/composables/use3DPerformance'

interface Props {
  count?: number
  color?: string
  size?: number
  speed?: number
  spread?: number
  opacity?: number
  animated?: boolean
  pattern?: 'random' | 'grid' | 'spiral' | 'wave'
}

const props = withDefaults(defineProps<Props>(), {
  count: 1000,
  color: '#00ff41',
  size: 2.0,
  speed: 0.5,
  spread: 50,
  opacity: 0.8,
  animated: true,
  pattern: 'random',
})

const groupRef = ref()
const pointsRef = ref()
const geometryRef = ref()
const materialRef = ref()

// Performance monitoring
const { qualitySettings, shouldEnableFeature } = use3DPerformance()

// Reactive particle count based on performance
const particleCount = computed(() => {
  if (!shouldEnableFeature('particles')) return 0
  return Math.min(props.count, qualitySettings.value.particleCount)
})

// Particle data arrays
const positions = ref<Float32Array>()
const velocities = ref<Float32Array>()
const originalPositions = ref<Float32Array>()
const scales = ref<Float32Array>()
const colors = ref<Float32Array>()

// Animation state
const time = ref(0)

// Initialize particles
function initializeParticles() {
  const count = particleCount.value
  if (count === 0) return

  // Wait for geometry ref to be available
  nextTick(() => {
    if (!geometryRef.value) return

    // Position arrays
    positions.value = new Float32Array(count * 3)
    velocities.value = new Float32Array(count * 3)
    originalPositions.value = new Float32Array(count * 3)
    scales.value = new Float32Array(count)
    colors.value = new Float32Array(count * 3)

    // Color conversion
    const baseColor = new Color(props.color)

  // Generate particles based on pattern
  for (let i = 0; i < count; i++) {
    const i3 = i * 3

    let x, y, z

    switch (props.pattern) {
      case 'grid':
        const gridSize = Math.cbrt(count)
        const gridIndex = i
        x = (gridIndex % gridSize - gridSize / 2) * (props.spread / gridSize)
        y = (Math.floor(gridIndex / gridSize) % gridSize - gridSize / 2) * (props.spread / gridSize)
        z = (Math.floor(gridIndex / (gridSize * gridSize)) - gridSize / 2) * (props.spread / gridSize)
        break

      case 'spiral':
        const angle = i * 0.1
        const radius = (i / count) * props.spread
        x = Math.cos(angle) * radius
        y = (i / count - 0.5) * props.spread
        z = Math.sin(angle) * radius
        break

      case 'wave':
        x = (i / count - 0.5) * props.spread * 2
        y = Math.sin(i * 0.1) * props.spread * 0.2
        z = (Math.random() - 0.5) * props.spread * 0.5
        break

      case 'random':
      default:
        x = (Math.random() - 0.5) * props.spread
        y = (Math.random() - 0.5) * props.spread
        z = (Math.random() - 0.5) * props.spread
        break
    }

    // Set positions
    positions.value[i3] = x
    positions.value[i3 + 1] = y
    positions.value[i3 + 2] = z

    // Store original positions for wave animations
    originalPositions.value[i3] = x
    originalPositions.value[i3 + 1] = y
    originalPositions.value[i3 + 2] = z

    // Random velocities
    velocities.value[i3] = (Math.random() - 0.5) * props.speed
    velocities.value[i3 + 1] = (Math.random() - 0.5) * props.speed
    velocities.value[i3 + 2] = (Math.random() - 0.5) * props.speed

    // Random scales
    scales.value[i] = Math.random() * 0.5 + 0.5

    // Color variations
    const colorVariation = Math.random() * 0.3 + 0.7
    colors.value[i3] = baseColor.r * colorVariation
    colors.value[i3 + 1] = baseColor.g * colorVariation
    colors.value[i3 + 2] = baseColor.b * colorVariation
  }

    // Set geometry attributes
    geometryRef.value.setAttribute('position', new BufferAttribute(positions.value, 3))
    geometryRef.value.setAttribute('color', new BufferAttribute(colors.value, 3))
  })
}

// Update particle animation
function updateParticles(delta: number) {
  if (!positions.value || !props.animated || particleCount.value === 0) return

  time.value += delta

  const count = particleCount.value

  for (let i = 0; i < count; i++) {
    const i3 = i * 3

    // Update positions based on velocities
    positions.value[i3] += velocities.value[i3] * delta
    positions.value[i3 + 1] += velocities.value[i3 + 1] * delta
    positions.value[i3 + 2] += velocities.value[i3 + 2] * delta

    // Add wave motion for cyberpunk effect
    const waveOffset = Math.sin(time.value * 2 + i * 0.1) * 0.5
    positions.value[i3 + 1] = originalPositions.value[i3 + 1] + waveOffset

    // Boundary wrapping
    const boundary = props.spread / 2
    if (Math.abs(positions.value[i3]) > boundary) {
      positions.value[i3] = originalPositions.value[i3]
    }
    if (Math.abs(positions.value[i3 + 2]) > boundary) {
      positions.value[i3 + 2] = originalPositions.value[i3 + 2]
    }
  }

  // Mark positions for update
  if (geometryRef.value) {
    geometryRef.value.attributes.position.needsUpdate = true
  }
}

// Render loop
const { onLoop } = useRenderLoop()

onLoop(({ delta }) => {
  updateParticles(delta)
})

// Initialize on mount
onMounted(() => {
  initializeParticles()
})

// Reinitialize when particle count changes
watch(particleCount, () => {
  initializeParticles()
})

// Watch for prop changes
watch(() => props.color, () => {
  initializeParticles()
})

watch(() => props.size, (newSize) => {
  if (materialRef.value) {
    materialRef.value.size = newSize
  }
})

watch(() => props.opacity, (newOpacity) => {
  if (materialRef.value) {
    materialRef.value.opacity = newOpacity
  }
})

defineExpose({
  group: groupRef,
  points: pointsRef,
  geometry: geometryRef,
  material: materialRef,
})
</script>
