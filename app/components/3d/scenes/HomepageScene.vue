<template>
  <TresCanvas
    :alpha="true"
    :antialias="true"
    :shadows="true"
    :clear-color="'#000000'"
    :window-size="true"
    class="w-full h-full"
  >
    <!-- Camera Setup -->
    <TresPerspectiveCamera
      :position="[0, 0, 10]"
      :fov="75"
      :look-at="[0, 0, 0]"
    />

    <!-- Lighting -->
    <TresAmbientLight :intensity="0.3" color="#ffffff" />
    <TresDirectionalLight
      :position="[10, 10, 5]"
      :intensity="0.8"
      :cast-shadow="true"
      color="#ffffff"
    />
    <!-- Cyberpunk Background Effects -->
    <CyberBackground
      :theme="backgroundTheme"
      :animated="!prefersReducedMotion"
      :interactive="true"
      :intensity="backgroundIntensity"
      :show-grid="showGrid"
      @background-interaction="onBackgroundInteraction"
    />

    <!-- Hero Section 3D Elements -->
    <TresGroup :position="[0, 2, 0]">
      <!-- Central Logo/Brand Element -->
      <TresMesh ref="logoRef" :position="[0, 0, 0]">
        <TresOctahedronGeometry :args="[2]" />
        <NeonMaterial
          :color="logoColor"
          :intensity="1.5"
          :glow-strength="3.0"
          :pulse-speed="0.8"
          :animated="!prefersReducedMotion"
        />
      </TresMesh>

      <!-- Orbiting Elements -->
      <TresGroup ref="orbitGroupRef">
        <TresMesh
          v-for="(orb, index) in orbitingElements"
          :key="index"
          :position="orb.position"
        >
          <TresSphereGeometry :args="[0.3, 16, 16]" />
          <NeonMaterial
            :color="orb.color"
            :intensity="1.0"
            :glow-strength="2.0"
            :pulse-speed="1.2 + index * 0.2"
            :animated="!prefersReducedMotion"
          />
        </TresMesh>
      </TresGroup>
    </TresGroup>

    <!-- Performance Metrics 3D Display -->
    <TresGroup v-if="showMetrics" :position="[-8, -2, 0]">
      <TresMesh
        v-for="(metric, index) in performanceMetrics"
        :key="metric.label"
        :position="[0, index * 1.5, 0]"
      >
        <TresBoxGeometry :args="[metric.value * 0.1, 0.2, 0.2]" />
        <NeonMaterial
          :color="metric.color"
          :intensity="0.8"
          :glow-strength="1.5"
          :animated="!prefersReducedMotion"
        />
      </TresMesh>
    </TresGroup>

    <!-- Interactive Elements -->
    <TresGroup :position="[8, 0, 0]">
      <TresMesh
        v-for="(element, index) in interactiveElements"
        :key="index"
        :position="element.position"
        @click="onElementClick(index)"
        @pointer-enter="onElementHover(index, true)"
        @pointer-leave="onElementHover(index, false)"
      >
        <component :is="element.geometry" v-bind="element.geometryProps" />
        <NeonMaterial
          :color="element.color"
          :intensity="element.intensity"
          :glow-strength="element.glowStrength"
          :animated="!prefersReducedMotion"
        />
      </TresMesh>
    </TresGroup>

  </TresCanvas>
</template>

<script setup lang="ts">
import { useRenderLoop } from '@tresjs/core'
import { usePreferredReducedMotion } from '@vueuse/core'
import { use3DPerformance } from '@/composables/use3DPerformance'
import CyberBackground from '@/components/3d/effects/CyberBackground.vue'
import NeonMaterial from '@/components/3d/base/NeonMaterial.vue'
import gsap from 'gsap'

interface Props {
  backgroundTheme?: 'neon-lime' | 'neon-cyan' | 'neon-pink' | 'neon-blue' | 'mixed'
  showMetrics?: boolean
  showGrid?: boolean
  logoColor?: string
  backgroundIntensity?: number
}

const props = withDefaults(defineProps<Props>(), {
  backgroundTheme: 'mixed',
  showMetrics: true,
  showGrid: true,
  logoColor: '#00ff41',
  backgroundIntensity: 1.0,
})

// Refs
const logoRef = ref()
const orbitGroupRef = ref()

// Reactive properties
const prefersReducedMotion = usePreferredReducedMotion()
const isDev = process.dev

// Performance monitoring
const { metrics } = use3DPerformance()

// Orbiting elements around the logo
const orbitingElements = ref([
  { position: [3, 0, 0], color: '#00ffff' },
  { position: [-3, 0, 0], color: '#ff006e' },
  { position: [0, 3, 0], color: '#0066ff' },
  { position: [0, -3, 0], color: '#39ff14' },
])

// Performance metrics for 3D visualization
const performanceMetrics = computed(() => [
  {
    label: 'FPS',
    value: Math.min(metrics.value.fps, 60),
    color: metrics.value.fps > 45 ? '#00ff41' : metrics.value.fps > 30 ? '#ffff00' : '#ff073a',
  },
  {
    label: 'Memory',
    value: Math.min(metrics.value.memoryUsage, 100),
    color: metrics.value.memoryUsage < 50 ? '#00ff41' : metrics.value.memoryUsage < 80 ? '#ffff00' : '#ff073a',
  },
  {
    label: 'Draw Calls',
    value: Math.min(metrics.value.drawCalls, 100),
    color: metrics.value.drawCalls < 50 ? '#00ff41' : metrics.value.drawCalls < 100 ? '#ffff00' : '#ff073a',
  },
])

// Interactive elements
const interactiveElements = ref([
  {
    position: [0, 2, 0],
    geometry: 'TresBoxGeometry',
    geometryProps: { args: [1, 1, 1] },
    color: '#00ff41',
    intensity: 1.0,
    glowStrength: 2.0,
  },
  {
    position: [0, 0, 0],
    geometry: 'TresSphereGeometry',
    geometryProps: { args: [0.5, 16, 16] },
    color: '#00ffff',
    intensity: 1.0,
    glowStrength: 2.0,
  },
  {
    position: [0, -2, 0],
    geometry: 'TresOctahedronGeometry',
    geometryProps: { args: [0.6] },
    color: '#ff006e',
    intensity: 1.0,
    glowStrength: 2.0,
  },
])

// Animation state
const time = ref(0)

// Update animations
function updateAnimations(delta: number) {
  if (prefersReducedMotion.value) return

  time.value += delta

  // Rotate logo
  if (logoRef.value) {
    logoRef.value.rotation.y = time.value * 0.5
    logoRef.value.rotation.x = Math.sin(time.value * 0.3) * 0.2
  }

  // Orbit elements around logo
  if (orbitGroupRef.value) {
    orbitGroupRef.value.rotation.y = time.value * 0.3
    orbitGroupRef.value.rotation.z = Math.sin(time.value * 0.2) * 0.1
  }
}

// Interaction handlers
function onElementClick(index: number) {
  const element = interactiveElements.value[index]

  // Pulse animation
  gsap.to(element, {
    intensity: 2.0,
    glowStrength: 4.0,
    duration: 0.3,
    yoyo: true,
    repeat: 1,
    ease: "power2.out"
  })

  emit('elementClick', { index, element })
}

function onElementHover(index: number, isHovering: boolean) {
  const element = interactiveElements.value[index]

  if (isHovering) {
    gsap.to(element, {
      intensity: 1.5,
      glowStrength: 3.0,
      duration: 0.2,
      ease: "power2.out"
    })
  } else {
    gsap.to(element, {
      intensity: 1.0,
      glowStrength: 2.0,
      duration: 0.2,
      ease: "power2.out"
    })
  }
}

function onBackgroundInteraction(event: { type: string; data: any }) {
  emit('backgroundInteraction', event)
}

// Render loop
const { onLoop } = useRenderLoop()

onLoop(({ delta }) => {
  updateAnimations(delta)
})

// Emit events
const emit = defineEmits<{
  elementClick: [{ index: number; element: any }]
  elementHover: [{ index: number; element: any; isHovering: boolean }]
  backgroundInteraction: [{ type: string; data: any }]
}>()

defineExpose({
  logo: logoRef,
  orbitGroup: orbitGroupRef,
  performanceMetrics,
  interactiveElements,
})
</script>
