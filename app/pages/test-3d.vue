<template>
  <div class="min-h-screen bg-brutal-black">
    <div class="container mx-auto p-8">
      <h1 class="font-brutal text-4xl text-acid-green mb-8">3D TEST PAGE</h1>
      
      <!-- Simple TresJS Test -->
      <div class="h-96 border-brutal bg-brutal-charcoal mb-8">
        <ClientOnly>
          <TresCanvas
            :alpha="true"
            :antialias="true"
            :clear-color="'#000000'"
            :window-size="true"
            class="w-full h-full"
          >
            <!-- Camera -->
            <TresPerspectiveCamera
              :position="[0, 0, 5]"
              :fov="75"
              :look-at="[0, 0, 0]"
            />
            
            <!-- Lighting -->
            <TresAmbientLight :intensity="0.5" color="#ffffff" />
            <TresDirectionalLight
              :position="[5, 5, 5]"
              :intensity="0.8"
              color="#ffffff"
            />
            
            <!-- Simple rotating cube -->
            <TresMesh ref="cubeRef">
              <TresBoxGeometry :args="[1, 1, 1]" />
              <TresMeshStandardMaterial :color="'#00ff41'" />
            </TresMesh>
            
            <!-- Test NeonMaterial -->
            <TresMesh :position="[2, 0, 0]">
              <TresSphereGeometry :args="[0.5, 16, 16]" />
              <NeonMaterial
                color="#00ffff"
                :intensity="1.5"
                :glow-strength="2.0"
                :pulse-speed="1.0"
                :animated="true"
              />
            </TresMesh>
            
            <!-- Test ParticleSystem -->
            <ParticleSystem
              :count="100"
              color="#ff006e"
              :size="2"
              :speed="0.5"
              :spread="10"
              :opacity="0.8"
              :animated="true"
              pattern="random"
            />
          </TresCanvas>
          
          <template #fallback>
            <div class="flex items-center justify-center w-full h-full">
              <div class="text-center">
                <h3 class="font-brutal text-xl text-acid-green mb-4">LOADING 3D...</h3>
                <p class="font-mono-brutal text-brutal-white">
                  WebGL is initializing
                </p>
              </div>
            </div>
          </template>
        </ClientOnly>
      </div>
      
      <!-- Performance Info -->
      <div class="border-brutal bg-brutal-white p-4">
        <h2 class="font-brutal text-xl text-brutal-black mb-4">PERFORMANCE METRICS</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="border-brutal bg-brutal-charcoal p-3">
            <div class="font-mono-brutal text-xs text-brutal-white">FPS</div>
            <div class="font-brutal text-lg text-acid-green">{{ Math.round(performanceMetrics.fps) }}</div>
          </div>
          <div class="border-brutal bg-brutal-charcoal p-3">
            <div class="font-mono-brutal text-xs text-brutal-white">MEMORY</div>
            <div class="font-brutal text-lg text-neon-cyan">{{ Math.round(performanceMetrics.memoryUsage) }}MB</div>
          </div>
          <div class="border-brutal bg-brutal-charcoal p-3">
            <div class="font-mono-brutal text-xs text-brutal-white">DRAW CALLS</div>
            <div class="font-brutal text-lg text-hot-magenta">{{ performanceMetrics.drawCalls }}</div>
          </div>
          <div class="border-brutal bg-brutal-charcoal p-3">
            <div class="font-mono-brutal text-xs text-brutal-white">QUALITY</div>
            <div class="font-brutal text-lg text-plasma-orange">{{ adaptiveQuality.toUpperCase() }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRenderLoop } from '@tresjs/core'
import { use3DPerformance } from '@/composables/use3DPerformance'
import NeonMaterial from '@/components/3d/base/NeonMaterial.vue'
import ParticleSystem from '@/components/3d/base/ParticleSystem.vue'

// SEO
useHead({
  title: '3D Test - DEFI.AI',
  meta: [
    {
      name: 'description',
      content: 'Testing TresJS 3D components and performance',
    },
  ],
})

// Refs
const cubeRef = ref()

// Performance monitoring
const { metrics, adaptiveQuality } = use3DPerformance()

// Reactive performance metrics
const performanceMetrics = computed(() => ({
  fps: metrics.value.fps,
  memoryUsage: metrics.value.memoryUsage,
  drawCalls: metrics.value.drawCalls,
  triangles: metrics.value.triangles,
}))

// Animation
const { onLoop } = useRenderLoop()

onLoop(({ delta }) => {
  // Rotate the cube
  if (cubeRef.value) {
    cubeRef.value.rotation.x += delta
    cubeRef.value.rotation.y += delta * 0.5
  }
})
</script>
